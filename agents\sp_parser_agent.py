import os
import json
from agents.llm_config import get_llm


class SPExtractorAgent:
    def __init__(self, prompt_path="ContextGuardrail/sp_parser_prompts.txt"):
        self.llm = get_llm()  # Directly get LLM callable from your config
        self.prompt_template = self._load_prompt_template(prompt_path)

    @staticmethod
    def _load_prompt_template(path):
        with open(path, "r", encoding="utf-8") as f:
            return f.read()

    def _build_prompt(self, procedure, template):
        required_columns = [col["FILE_COLUMN_NAME"].strip() for col in template]
        data_types = {col["FILE_COLUMN_NAME"].strip(): col["Data_Type"] for col in template}

        return self.prompt_template.format(
            required_columns=json.dumps(required_columns, indent=2),
            data_types=json.dumps(data_types, indent=2),
            procedure_text=procedure["procedure_definition"],
            procedure_name=procedure["procedure_name"]
        )

    def extract_rules(self, procedure, template):
        prompt = self._build_prompt(procedure, template)
        try:
            response = self.llm(prompt)
            result = json.loads(response)
            return {
                "schema_validation": result.get("schema_validation", []),
                "data_validation": result.get("data_validation", [])
            }
        except Exception as e:
            print(f"❌ Error processing {procedure['procedure_name']}: {e}")
            return {"schema_validation": [], "data_validation": []}

    def process_procedures(self, procedures, column_template, output_dir="outputs"):
        schema_rules, data_rules = [], []

        for proc in procedures:
            print(f"⏳ Processing {proc['procedure_name']}...")
            result = self.extract_rules(proc, column_template)
            schema_rules.extend(result["schema_validation"])
            data_rules.extend(result["data_validation"])

        os.makedirs(output_dir, exist_ok=True)
        self._save_json(schema_rules, os.path.join(output_dir, "schema_validation.json"))
        self._save_json(data_rules, os.path.join(output_dir, "data_validation.json"))

        print(f"✅ Completed. Outputs saved in: {output_dir}")

    @staticmethod
    def _save_json(data, path):
        with open(path, "w") as f:
            json.dump(data, f, indent=2)


def main():
    extractor = SPExtractorAgent()

    with open("data/inputs/stored_procedures.json", "r") as f:
        procedures = json.load(f)

    with open("data/inputs/FundHolding_Metadata_Columns 1.json", "r") as f:
        column_template = json.load(f)

    extractor.process_procedures(procedures, column_template)


if __name__ == "__main__":
    main()
