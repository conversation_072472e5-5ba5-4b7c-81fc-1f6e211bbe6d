from agents.llm_config import get_llm


class GuardRailAgent:
    def __init__(self):
        self.llm = get_llm()

    def check(self, failure_reason: str) -> dict:
        prompt = f"""
You are a strict validation expert. Your job is to check if the following failure reason indicates a valid failure or is caused by hallucination, irrelevant errors, or sensitive data leak.

Failure Reason:
"{failure_reason}"

Respond only with:
VALID - if the failure reason is genuine and process can stop.
INVALID - if the reason looks suspicious, hallucinated, or irrelevant and process should not stop.

Now analyze and answer.
"""
        response = self.llm(prompt)
        if "INVALID" in response:
            return {"valid": False, "message": "Guardrail blocked due to invalid failure reason."}
        return {"valid": True, "message": "Failure reason is valid."}
