2025-07-14 20:41:39,761 - INFO - [RuleAgent] Reading metadata file: data\inputs\FundHolding_Metadata_Columns 1.xlsx
2025-07-14 20:41:44,343 - INFO - [RuleAgent]  Excel loaded with 10 rows and 31 columns.
2025-07-14 20:41:44,353 - INFO - [RuleAgent]  Extracted columns: ['UNIQUE_ID  ', 'PORTFOLIO_ID  ', 'REGISTERED_HOLDER  ', 'NAV  ', 'OWNERSHIP_PERCENTAGE  ', 'CAPITAL_CALLED  ', 'NO_OF_SHARES  ', 'COMMITTED_CAPITAL  ', 'PERIOD  ', 'FUND_NAME  ']
2025-07-14 20:41:44,354 - INFO - [RuleAgent]  Invoking LLM for schema extraction...
2025-07-14 20:41:44,354 - DEBUG - [RuleAgent]  LLM PROMPT:

You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{
  "columns": [
    {
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    ...
  ]
}

Here are the column names:
[
  "UNIQUE_ID  ",
  "PORTFOLIO_ID  ",
  "REGISTERED_HOLDER  ",
  "NAV  ",
  "OWNERSHIP_PERCENTAGE  ",
  "CAPITAL_CALLED  ",
  "NO_OF_SHARES  ",
  "COMMITTED_CAPITAL  ",
  "PERIOD  ",
  "FUND_NAME  "
]

2025-07-14 20:41:44,355 - ERROR - [RuleAgent]  Failed to parse LLM response: 'function' object has no attribute 'invoke'
2025-07-14 20:43:21,897 - INFO - [RuleAgent] Reading metadata file: data\inputs\FundHolding_Metadata_Columns 1.xlsx
2025-07-14 20:43:22,853 - INFO - [RuleAgent]  Excel loaded with 10 rows and 31 columns.
2025-07-14 20:43:22,859 - INFO - [RuleAgent]  Extracted columns: ['UNIQUE_ID  ', 'PORTFOLIO_ID  ', 'REGISTERED_HOLDER  ', 'NAV  ', 'OWNERSHIP_PERCENTAGE  ', 'CAPITAL_CALLED  ', 'NO_OF_SHARES  ', 'COMMITTED_CAPITAL  ', 'PERIOD  ', 'FUND_NAME  ']
2025-07-14 20:43:22,860 - INFO - [RuleAgent]  Invoking LLM for schema extraction...
2025-07-14 20:43:22,860 - DEBUG - [RuleAgent]  LLM PROMPT:

You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{
  "columns": [
    {
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    ...
  ]
}

Here are the column names:
[
  "UNIQUE_ID  ",
  "PORTFOLIO_ID  ",
  "REGISTERED_HOLDER  ",
  "NAV  ",
  "OWNERSHIP_PERCENTAGE  ",
  "CAPITAL_CALLED  ",
  "NO_OF_SHARES  ",
  "COMMITTED_CAPITAL  ",
  "PERIOD  ",
  "FUND_NAME  "
]

2025-07-14 20:43:22,860 - ERROR - [RuleAgent]  Failed to parse LLM response: 'function' object has no attribute 'invoke'
2025-07-14 20:48:46,397 - INFO - [RuleAgent] Reading metadata file: data\inputs\FundHolding_Metadata_Columns 1.xlsx
2025-07-14 20:48:47,650 - INFO - [RuleAgent]  Excel loaded with 10 rows and 31 columns.
2025-07-14 20:48:47,656 - INFO - [RuleAgent]  Extracted columns: ['UNIQUE_ID  ', 'PORTFOLIO_ID  ', 'REGISTERED_HOLDER  ', 'NAV  ', 'OWNERSHIP_PERCENTAGE  ', 'CAPITAL_CALLED  ', 'NO_OF_SHARES  ', 'COMMITTED_CAPITAL  ', 'PERIOD  ', 'FUND_NAME  ']
2025-07-14 20:48:47,657 - INFO - [RuleAgent]  Invoking LLM for schema extraction...
2025-07-14 20:48:47,657 - DEBUG - [RuleAgent]  LLM PROMPT:

You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{
  "columns": [
    {
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    ...
  ]
}

Here are the column names:
[
  "UNIQUE_ID  ",
  "PORTFOLIO_ID  ",
  "REGISTERED_HOLDER  ",
  "NAV  ",
  "OWNERSHIP_PERCENTAGE  ",
  "CAPITAL_CALLED  ",
  "NO_OF_SHARES  ",
  "COMMITTED_CAPITAL  ",
  "PERIOD  ",
  "FUND_NAME  "
]

2025-07-14 20:48:47,657 - ERROR - [RuleAgent]  Failed to parse LLM response: 'function' object has no attribute 'invoke'
2025-07-14 20:52:18,282 - INFO - [RuleAgent] Reading metadata file: data\inputs\FundHolding_Metadata_Columns 1.xlsx
2025-07-14 20:52:19,446 - INFO - [RuleAgent]  Excel loaded with 10 rows and 31 columns.
2025-07-14 20:52:19,454 - INFO - [RuleAgent]  Extracted columns: ['UNIQUE_ID  ', 'PORTFOLIO_ID  ', 'REGISTERED_HOLDER  ', 'NAV  ', 'OWNERSHIP_PERCENTAGE  ', 'CAPITAL_CALLED  ', 'NO_OF_SHARES  ', 'COMMITTED_CAPITAL  ', 'PERIOD  ', 'FUND_NAME  ']
2025-07-14 20:52:19,454 - INFO - [RuleAgent]  Invoking LLM for schema extraction...
2025-07-14 20:52:19,455 - DEBUG - [RuleAgent]  LLM PROMPT:

You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{
  "columns": [
    {
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    ...
  ]
}

Here are the column names:
[
  "UNIQUE_ID  ",
  "PORTFOLIO_ID  ",
  "REGISTERED_HOLDER  ",
  "NAV  ",
  "OWNERSHIP_PERCENTAGE  ",
  "CAPITAL_CALLED  ",
  "NO_OF_SHARES  ",
  "COMMITTED_CAPITAL  ",
  "PERIOD  ",
  "FUND_NAME  "
]

2025-07-14 20:52:31,497 - DEBUG - [RuleAgent]  LLM RAW RESPONSE:
{
  "columns": [
    {
      "column_name": "UNIQUE_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    {
      "column_name": "PORTFOLIO_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 2
    },
    {
      "column_name": "REGISTERED_HOLDER",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 3
    },
    {
      "column_name": "NAV",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 4
    },
    {
      "column_name": "OWNERSHIP_PERCENTAGE",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 5
    },
    {
      "column_name": "CAPITAL_CALLED",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 6
    },
    {
      "column_name": "NO_OF_SHARES",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 7
    },
    {
      "column_name": "COMMITTED_CAPITAL",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 8
    },
    {
      "column_name": "PERIOD",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 9
    },
    {
      "column_name": "FUND_NAME",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 10
    }
  ]
}
2025-07-14 20:52:31,497 - INFO - [RuleAgent]  Extracted 10 schema columns from LLM.
2025-07-14 20:52:31,497 - INFO - [RuleAgent]  Saving schema to file...
2025-07-14 20:52:31,501 - INFO - [RuleAgent]  Schema saved to: store_procedure/enhanced_validation_rules.json
2025-07-14 20:54:08,856 - INFO - [RuleAgent] Reading metadata file: data\inputs\FundHolding_Metadata_Columns 1.xlsx
2025-07-14 20:54:10,791 - INFO - [RuleAgent]  Excel loaded with 10 rows and 31 columns.
2025-07-14 20:54:10,801 - INFO - [RuleAgent]  Extracted columns: ['UNIQUE_ID  ', 'PORTFOLIO_ID  ', 'REGISTERED_HOLDER  ', 'NAV  ', 'OWNERSHIP_PERCENTAGE  ', 'CAPITAL_CALLED  ', 'NO_OF_SHARES  ', 'COMMITTED_CAPITAL  ', 'PERIOD  ', 'FUND_NAME  ']
2025-07-14 20:54:10,802 - INFO - [RuleAgent]  Invoking LLM for schema extraction...
2025-07-14 20:54:10,802 - DEBUG - [RuleAgent]  LLM PROMPT:

You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{
  "columns": [
    {
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    ...
  ]
}

Here are the column names:
[
  "UNIQUE_ID  ",
  "PORTFOLIO_ID  ",
  "REGISTERED_HOLDER  ",
  "NAV  ",
  "OWNERSHIP_PERCENTAGE  ",
  "CAPITAL_CALLED  ",
  "NO_OF_SHARES  ",
  "COMMITTED_CAPITAL  ",
  "PERIOD  ",
  "FUND_NAME  "
]

2025-07-14 20:54:19,354 - DEBUG - [RuleAgent]  LLM RAW RESPONSE:
{
  "columns": [
    {
      "column_name": "UNIQUE_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    {
      "column_name": "PORTFOLIO_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 2
    },
    {
      "column_name": "REGISTERED_HOLDER",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 3
    },
    {
      "column_name": "NAV",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 4
    },
    {
      "column_name": "OWNERSHIP_PERCENTAGE",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 5
    },
    {
      "column_name": "CAPITAL_CALLED",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 6
    },
    {
      "column_name": "NO_OF_SHARES",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 7
    },
    {
      "column_name": "COMMITTED_CAPITAL",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 8
    },
    {
      "column_name": "PERIOD",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 9
    },
    {
      "column_name": "FUND_NAME",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 10
    }
  ]
}
2025-07-14 20:54:19,356 - INFO - [RuleAgent]  Extracted 10 schema columns from LLM.
2025-07-14 20:54:19,356 - INFO - [RuleAgent]  Saving schema to file...
2025-07-14 20:54:19,364 - INFO - [RuleAgent]  Schema saved to: store_procedure/enhanced_validation_rules.json
2025-07-14 21:05:20,416 - INFO - [RuleAgent] Reading metadata file: data\inputs\FundHolding_Metadata_Columns 1.xlsx
2025-07-14 21:05:21,534 - INFO - [RuleAgent]  Excel loaded with 10 rows and 31 columns.
2025-07-14 21:05:21,542 - INFO - [RuleAgent]  Extracted columns: ['UNIQUE_ID  ', 'PORTFOLIO_ID  ', 'REGISTERED_HOLDER  ', 'NAV  ', 'OWNERSHIP_PERCENTAGE  ', 'CAPITAL_CALLED  ', 'NO_OF_SHARES  ', 'COMMITTED_CAPITAL  ', 'PERIOD  ', 'FUND_NAME  ']
2025-07-14 21:05:21,542 - INFO - [RuleAgent]  Invoking LLM for schema extraction...
2025-07-14 21:05:21,542 - DEBUG - [RuleAgent]  LLM PROMPT:

You are a schema extraction agent.

From the following list of column names, generate a JSON schema where:
- Each column must include: "column_name", "data_type", "is_required", "nullable", "position"
- "data_type" must be either "STRING" or "NUMBER"
- All columns are required and cannot be null (i.e., is_required: true, nullable: false)
- Start "position" from 1 and increment for each column

Respond ONLY with JSON in this format:
{
  "columns": [
    {
      "column_name": "COLUMN_1",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    ...
  ]
}

Here are the column names:
[
  "UNIQUE_ID  ",
  "PORTFOLIO_ID  ",
  "REGISTERED_HOLDER  ",
  "NAV  ",
  "OWNERSHIP_PERCENTAGE  ",
  "CAPITAL_CALLED  ",
  "NO_OF_SHARES  ",
  "COMMITTED_CAPITAL  ",
  "PERIOD  ",
  "FUND_NAME  "
]

2025-07-14 21:05:30,417 - DEBUG - [RuleAgent]  LLM RAW RESPONSE:
{
  "columns": [
    {
      "column_name": "UNIQUE_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 1
    },
    {
      "column_name": "PORTFOLIO_ID",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 2
    },
    {
      "column_name": "REGISTERED_HOLDER",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 3
    },
    {
      "column_name": "NAV",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 4
    },
    {
      "column_name": "OWNERSHIP_PERCENTAGE",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 5
    },
    {
      "column_name": "CAPITAL_CALLED",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 6
    },
    {
      "column_name": "NO_OF_SHARES",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 7
    },
    {
      "column_name": "COMMITTED_CAPITAL",
      "data_type": "NUMBER",
      "is_required": true,
      "nullable": false,
      "position": 8
    },
    {
      "column_name": "PERIOD",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 9
    },
    {
      "column_name": "FUND_NAME",
      "data_type": "STRING",
      "is_required": true,
      "nullable": false,
      "position": 10
    }
  ]
}
2025-07-14 21:05:30,417 - INFO - [RuleAgent]  Extracted 10 schema columns from LLM.
2025-07-14 21:05:30,418 - INFO - [RuleAgent]  Saving schema to file...
2025-07-14 21:05:30,422 - INFO - [RuleAgent]  Schema saved to: store_procedure/enhanced_validation_rules.json
