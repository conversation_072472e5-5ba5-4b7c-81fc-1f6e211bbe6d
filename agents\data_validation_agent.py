import os
import json
import yaml
import re
import pandas as pd
from dotenv import load_dotenv
from typing import List, Dict
from custom_logger import get_logger
from openai import OpenAI

# ==========================
# Load environment & config
# ==========================
load_dotenv()
with open("config/model_config.yaml", "r") as f:
    model_config = yaml.safe_load(f)

MODEL_NAME = model_config["openai"]["model"]
TEMPERATURE = model_config["openai"].get("temperature", 0.2)
API_KEY = os.getenv("OPENAI_API_KEY")

client = OpenAI(api_key=API_KEY)
logger = get_logger("DataValidationAgent", "logs/data_validation.log")

# =====================
# Prompt / Rule Loaders
# =====================
def load_prompt(path: str) -> str:
    with open(path, "r", encoding="utf-8") as f:
        return f.read()

def load_rules(path: str) -> List[Dict]:
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)

# ===================
# Parse LLM responses
# ===================
def parse_llm_response(text: str) -> Dict:
    try:
        json_match = re.search(r"\{.*?\}", text.strip(), re.DOTALL)
        if not json_match:
            raise ValueError("No valid JSON object found.")
        json_str = json_match.group(0)
        result = json.loads(json_str)

        # Fix for improperly quoted booleans
        if isinstance(result.get("is_correct"), str):
            result["is_correct"] = result["is_correct"].strip().lower() == "true"

        return result
    except Exception as e:
        logger.error(f"⚠️ LLM response parse error: {e}\nRaw content: {text}")
        return {"is_correct": False, "why": f"Invalid LLM response: {text}"}

# ===============
# LLM Call Helper
# ===============
def call_llm(prompt: str) -> Dict:
    response = client.chat.completions.create(
        model=MODEL_NAME,
        messages=[
            {"role": "system", "content": "You are a data validation expert."},
            {"role": "user", "content": prompt}
        ],
        temperature=TEMPERATURE
    )
    raw_response = response.choices[0].message.content
    print("🧠 Raw LLM response:", raw_response)
    return parse_llm_response(raw_response)

# ===================
# Main Execution Flow
# ===================
def main():
    try:
        ownership_prompt_template = load_prompt("ContextGuardrail/ownership_percentage_prompt.txt")
        data_validation_prompt_template = load_prompt("ContextGuardrail/data_validation_prompts.txt")
        validation_rules = load_rules("data/outputs/data_validation.json")
    except Exception as e:
        print(f"❌ Failed to load prompt/config: {e}")
        logger.error(f"Prompt/Rule load error: {e}")
        return

    file_path = input("📂 Enter path to Excel file to validate: ").strip()
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        logger.error(f"Missing file: {file_path}")
        return

    try:
        df = pd.read_excel(file_path)
    except Exception as e:
        print(f"❌ Failed to read Excel: {e}")
        logger.error(f"Read error: {e}")
        return

    # ===============
    # Step 1: Ownership Validation
    # ===============
    try:
        columns_list = df.columns.tolist()
        ownership_values = df["OWNERSHIP_PERCENTAGE"].fillna(0).astype(float).tolist() if "OWNERSHIP_PERCENTAGE" in df.columns else []

        ownership_prompt = ownership_prompt_template.format(
            ownership_percentages=json.dumps(ownership_values, indent=2),
            columns=json.dumps(columns_list, indent=2)
        )

        ownership_result = call_llm(ownership_prompt)

        if not ownership_result.get("is_correct", False):
            print(f"❌ Ownership validation failed: {ownership_result.get('why')}")
            logger.warning("Ownership validation failed. Halting.")
            return
        else:
            print(f"✅ Ownership validation passed: {ownership_result.get('why')}")
    except Exception as e:
        print(f"❌ Ownership validation error: {e}")
        logger.error(f"Ownership validation exception: {e}")
        return

    # ==================
    # Step 2: Row-level Validation
    # ==================
    validation_results = []

    for idx, row in df.iterrows():
        row_data = row.to_dict()

        prompt = data_validation_prompt_template.format(
            validation_rules=json.dumps(validation_rules, indent=2),
            row_data=json.dumps(row_data, indent=2),
            row_index=idx
        )

        try:
            result = call_llm(prompt)
            is_correct = result.get("is_correct", False)
            reason = result.get("why", "No reason given.")
        except Exception as e:
            is_correct = False
            reason = f"LLM error: {str(e)}"
            logger.error(f"Row {idx} validation error: {e}")

        row_data["is_correct"] = is_correct
        row_data["reason"] = reason
        validation_results.append(row_data)

        print(f"🧪 Row {idx + 1} validated: {'✔' if is_correct else '❌'}")

    # =====================
    # Save Outputs (JSON + XLSX)
    # =====================
    os.makedirs("data/outputs", exist_ok=True)

    json_path = "data/outputs/validation_results.json"
    xlsx_path = "data/outputs/validation_results.xlsx"

    with open(json_path, "w", encoding="utf-8") as jf:
        json.dump(validation_results, jf, indent=2)

    pd.DataFrame(validation_results).to_excel(xlsx_path, index=False)

    print(f"\n📁 JSON saved to: {json_path}")
    print(f"📁 Excel saved to: {xlsx_path}")
    print("✅ All validations completed successfully.")


if __name__ == "__main__":
    main()
