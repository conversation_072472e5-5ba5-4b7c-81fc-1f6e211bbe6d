import sys
import os

# Add project root to sys.path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from agents.master_agent import MasterValidationAgent


def main():
    # Sample test file paths
    metadata_excel_path = "data\inputs\FundHolding_Metadata_Columns 1.xlsx"
    user_excel_path = "data\inputs\FundHoldings_Data_Correct.xlsx"
    stored_procedure_file_path = "data\inputs\stored_procedures.json"

    # Initialize Master Agent
    master_agent = MasterValidationAgent()

    result = master_agent.run(
        metadata_excel_path=metadata_excel_path,
        user_excel_path=user_excel_path,
        stored_procedure_file_path=stored_procedure_file_path
    )

    print("\n====== FINAL RESULT ======")
    print(result)

    if result.get("success"):
        print("\n🎉 All validation stages passed successfully.")
    else:
        print("\n❌ Validation failed:", result.get("message"))


if __name__ == "__main__":
    main()