import json
import os
import pandas as pd
from agents.llm_config import get_llm
from agents.models import SchemaDefinition
from logging import get_logger
from pydantic import ValidationError
from datetime import datetime


class FileValidationAgent:
    def __init__(self):
        self.llm = get_llm()  # Simple callable: response = self.llm(prompt)
        self.logger = get_logger("FileValidationAgent", "logs/file_validation.log")

    def run(self, file_path: str, rule_path: str):
        result = {
            "success": True,
            "message": ""
        }

        file_name = os.path.basename(file_path)
        ext = os.path.splitext(file_path)[1].lower()

        self.logger.info(f"[FileAgent] File received: {file_name}")
        self.logger.info(f"[FileAgent] Detected file extension: {ext}")

        try:
            self.logger.info("[FileAgent] [STEP] Reading Excel file...")
            df = self._read_excel_with_engine(file_path, ext)
            self.logger.info("[FileAgent] Excel file loaded.")
            preview = json.loads(df.head(5).to_json(orient="records", date_format="iso"))
            actual_columns = df.columns.str.strip().str.upper().tolist()
            self.logger.info(f"[FileAgent] DataFrame shape: {df.shape}")
            self.logger.info(f"[FileAgent] Extracted Columns: {actual_columns}")
        except Exception as e:
            self.logger.error(f"[FileAgent] Could not read Excel file: {e}")
            result.update({"success": False, "message": str(e)})
            return result

        try:
            self.logger.info("[FileAgent] Loading and parsing schema definition...")
            with open(rule_path, "r", encoding="utf-8") as f:
                schema_json = json.load(f)

            schema_model = SchemaDefinition.parse_obj(schema_json["schema_definition"])
            self.logger.info("[FileAgent] Schema validated using Pydantic.")
        except (KeyError, ValidationError, Exception) as e:
            self.logger.error(f"[FileAgent] Failed to load/parse schema: {e}")
            return {"success": False, "message": "Invalid schema format"}

        prompt = self._build_prompt(schema_model, actual_columns, preview)
        self.logger.debug(f"[FileAgent] LLM Prompt:\n{prompt}")

        # Save prompt for traceability
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        prompt_log_path = f"logs/llm_prompt_{timestamp}.txt"
        response_log_path = f"logs/llm_output_{timestamp}.txt"

        try:
            with open(prompt_log_path, "w", encoding="utf-8") as pf:
                pf.write(prompt)
            self.logger.info(f"[FileAgent] LLM prompt saved to: {prompt_log_path}")
        except Exception as e:
            self.logger.warning(f"[FileAgent] Failed to save LLM prompt: {e}")

        try:
            self.logger.info("[FileAgent] Invoking LLM for schema validation...")
            response = self.llm(prompt)  # Correct invocation
            self.logger.debug(f"[FileAgent] LLM Response:\n{response}")

            with open(response_log_path, "w", encoding="utf-8") as rf:
                rf.write(response)
            self.logger.info(f"[FileAgent] LLM response saved to: {response_log_path}")

            if "INVALID" in response.upper():
                result["success"] = False

            result["message"] = response
        except Exception as e:
            self.logger.error(f"[FileAgent] LLM validation failed: {e}")
            result.update({"success": False, "message": str(e)})

        return result

    def _read_excel_with_engine(self, file_path, ext):
        if ext == ".xls":
            return pd.read_excel(file_path, engine='xlrd')
        elif ext == ".xlsx":
            return pd.read_excel(file_path, engine='openpyxl')
        else:
            raise ValueError(f"Unsupported file extension: {ext}")

    def _build_prompt(self, schema_model: SchemaDefinition, actual_columns: list, preview: list) -> str:
        schema_dict = schema_model.dict()
        return f"""
You are a file validation agent.

You will validate the uploaded Excel file based on:
1. Predefined schema (columns, data types, required fields, nullable)
2. Actual columns in the uploaded file
3. Sample preview of the data

Expected Schema:
{json.dumps(schema_dict, indent=2)}

Actual Columns in File:
{json.dumps(actual_columns, indent=2)}

Sample Data:
{json.dumps(preview, indent=2)}

Instructions:
- Highlight missing or extra columns.
- Ensure column order matches expected 'position'.
- Validate types (STRING vs NUMBER).
- Strictly enforce non-null and required fields.
- Do not assume anything beyond this input.

End with a clear verdict:
- VALID (if file structure is acceptable)
- INVALID (if any issues found)
"""
