2025-07-14 20:41:39,761 - INFO - [MASTER] Starting Multi-Agent Orchestration
2025-07-14 20:41:44,356 - INFO - [RULE AGENT] {'success': False, 'message': "LLM extraction failed: 'function' object has no attribute 'invoke'", 'schema': []}
2025-07-14 20:43:21,897 - INFO - [MASTER] Starting Multi-Agent Orchestration
2025-07-14 20:43:22,860 - INFO - [RULE AGENT] {'success': False, 'message': "LLM extraction failed: 'function' object has no attribute 'invoke'", 'schema': []}
2025-07-14 20:48:46,397 - INFO - [MASTER] Starting Multi-Agent Orchestration
2025-07-14 20:48:47,658 - INFO - [RULE AGENT] {'success': False, 'message': "LLM extraction failed: 'function' object has no attribute 'invoke'", 'schema': []}
2025-07-14 20:52:18,280 - INFO - [MASTER] Starting Multi-Agent Orchestration
2025-07-14 20:52:31,503 - INFO - [RULE AGENT] {'success': True, 'message': '<PERSON>hema extracted and saved successfully.', 'schema': {'columns': [{'column_name': 'UNIQUE_ID', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 1}, {'column_name': 'PORTFOLIO_ID', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 2}, {'column_name': 'REGISTERED_HOLDER', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 3}, {'column_name': 'NAV', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 4}, {'column_name': 'OWNERSHIP_PERCENTAGE', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 5}, {'column_name': 'CAPITAL_CALLED', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 6}, {'column_name': 'NO_OF_SHARES', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 7}, {'column_name': 'COMMITTED_CAPITAL', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 8}, {'column_name': 'PERIOD', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 9}, {'column_name': 'FUND_NAME', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 10}]}, 'schema_path': 'store_procedure/enhanced_validation_rules.json'}
2025-07-14 20:52:39,150 - INFO - [FILE VALIDATION AGENT] {'success': False, 'message': '### Validation Report\n\n1. **Missing or Extra Columns**:\n   - **Missing Columns**: None\n   - **Extra Columns**: None\n\n2. **Column Order**:\n   - The order of columns in the uploaded file matches the expected position.\n\n3. **Data Types Validation**:\n   - **UNIQUE_ID**: STRING (Valid)\n   - **PORTFOLIO_ID**: STRING (Valid)\n   - **REGISTERED_HOLDER**: STRING (Valid)\n   - **NAV**: NUMBER (Valid)\n   - **OWNERSHIP_PERCENTAGE**: NUMBER (Invalid - contains a null value in the third record)\n   - **CAPITAL_CALLED**: NUMBER (Valid)\n   - **NO_OF_SHARES**: NUMBER (Valid)\n   - **COMMITTED_CAPITAL**: NUMBER (Valid)\n   - **PERIOD**: STRING (Valid)\n   - **FUND_NAME**: STRING (Valid)\n\n4. **Required Fields Validation**:\n   - **UNIQUE_ID**: Present (Valid)\n   - **PORTFOLIO_ID**: Present (Valid)\n   - **REGISTERED_HOLDER**: Present (Valid)\n   - **NAV**: Present (Valid)\n   - **OWNERSHIP_PERCENTAGE**: Present but contains a null value (Invalid)\n   - **CAPITAL_CALLED**: Present (Valid)\n   - **NO_OF_SHARES**: Present (Valid)\n   - **COMMITTED_CAPITAL**: Present (Valid)\n   - **PERIOD**: Present (Valid)\n   - **FUND_NAME**: Present (Valid)\n\n### Verdict:\n**INVALID** (The file contains a null value in the required field "OWNERSHIP_PERCENTAGE".)'}
2025-07-14 20:54:08,855 - INFO - [MASTER] Starting Multi-Agent Orchestration
2025-07-14 20:54:19,365 - INFO - [RULE AGENT] {'success': True, 'message': 'Schema extracted and saved successfully.', 'schema': {'columns': [{'column_name': 'UNIQUE_ID', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 1}, {'column_name': 'PORTFOLIO_ID', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 2}, {'column_name': 'REGISTERED_HOLDER', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 3}, {'column_name': 'NAV', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 4}, {'column_name': 'OWNERSHIP_PERCENTAGE', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 5}, {'column_name': 'CAPITAL_CALLED', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 6}, {'column_name': 'NO_OF_SHARES', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 7}, {'column_name': 'COMMITTED_CAPITAL', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 8}, {'column_name': 'PERIOD', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 9}, {'column_name': 'FUND_NAME', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 10}]}, 'schema_path': 'store_procedure/enhanced_validation_rules.json'}
2025-07-14 20:54:25,471 - INFO - [FILE VALIDATION AGENT] {'success': False, 'message': '### Validation Report\n\n#### 1. Missing or Extra Columns:\n- **Missing Columns:** None\n- **Extra Columns:** None\n\n#### 2. Column Order:\n- The actual columns in the file match the expected order based on the predefined schema.\n\n#### 3. Data Types:\n- **UNIQUE_ID:** STRING (Valid)\n- **PORTFOLIO_ID:** STRING (Valid)\n- **REGISTERED_HOLDER:** STRING (Valid)\n- **NAV:** NUMBER (Valid)\n- **OWNERSHIP_PERCENTAGE:** NUMBER (Valid, but contains a null value in one record)\n- **CAPITAL_CALLED:** NUMBER (Valid)\n- **NO_OF_SHARES:** NUMBER (Valid)\n- **COMMITTED_CAPITAL:** NUMBER (Valid)\n- **PERIOD:** STRING (Valid)\n- **FUND_NAME:** STRING (Valid)\n\n#### 4. Required Fields:\n- **UNIQUE_ID:** Present and non-null (Valid)\n- **PORTFOLIO_ID:** Present and non-null (Valid)\n- **REGISTERED_HOLDER:** Present and non-null (Valid)\n- **NAV:** Present and non-null (Valid)\n- **OWNERSHIP_PERCENTAGE:** Present but contains a null value in one record (Invalid)\n- **CAPITAL_CALLED:** Present and non-null (Valid)\n- **NO_OF_SHARES:** Present and non-null (Valid)\n- **COMMITTED_CAPITAL:** Present and non-null (Valid)\n- **PERIOD:** Present and non-null (Valid)\n- **FUND_NAME:** Present and non-null (Valid)\n\n### Verdict:\n**INVALID** (The file contains a null value in the required field "OWNERSHIP_PERCENTAGE".)'}
2025-07-14 21:05:20,414 - INFO - [MASTER] Starting Multi-Agent Orchestration
2025-07-14 21:05:30,423 - INFO - [RULE AGENT] {'success': True, 'message': 'Schema extracted and saved successfully.', 'schema': {'columns': [{'column_name': 'UNIQUE_ID', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 1}, {'column_name': 'PORTFOLIO_ID', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 2}, {'column_name': 'REGISTERED_HOLDER', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 3}, {'column_name': 'NAV', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 4}, {'column_name': 'OWNERSHIP_PERCENTAGE', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 5}, {'column_name': 'CAPITAL_CALLED', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 6}, {'column_name': 'NO_OF_SHARES', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 7}, {'column_name': 'COMMITTED_CAPITAL', 'data_type': 'NUMBER', 'is_required': True, 'nullable': False, 'position': 8}, {'column_name': 'PERIOD', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 9}, {'column_name': 'FUND_NAME', 'data_type': 'STRING', 'is_required': True, 'nullable': False, 'position': 10}]}, 'schema_path': 'store_procedure/enhanced_validation_rules.json'}
2025-07-14 21:05:34,773 - INFO - [FILE VALIDATION AGENT] {'success': True, 'message': '### Validation Report\n\n#### 1. Missing or Extra Columns:\n- **Missing Columns:** None\n- **Extra Columns:** None\n\n#### 2. Column Order:\n- The actual columns in the file match the expected order based on their defined positions.\n\n#### 3. Data Types Validation:\n- **UNIQUE_ID:** STRING (Valid)\n- **PORTFOLIO_ID:** STRING (Valid)\n- **REGISTERED_HOLDER:** STRING (Valid)\n- **NAV:** NUMBER (Valid)\n- **OWNERSHIP_PERCENTAGE:** NUMBER (Valid)\n- **CAPITAL_CALLED:** NUMBER (Valid)\n- **NO_OF_SHARES:** NUMBER (Valid)\n- **COMMITTED_CAPITAL:** NUMBER (Valid)\n- **PERIOD:** STRING (Valid)\n- **FUND_NAME:** STRING (Valid)\n\n#### 4. Non-null and Required Fields:\n- All required fields are present and contain non-null values in the sample data.\n\n### Verdict:\n**VALID**'}
