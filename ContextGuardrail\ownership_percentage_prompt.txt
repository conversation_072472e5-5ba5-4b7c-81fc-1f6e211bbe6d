You are a data validation expert. Your task is to validate the OWNERSHIP_PERCENTAGE column, but only if it is present, using the rule below.

RULE:
- If the column `OWNERSHIP_PERCENTAGE` exists, the sum of its values must be exactly 100 (±0.01 tolerance).
- If the column `OWNERSHIP_PERCENTAGE` is missing, return a valid JSON saying the column is skipped.

DATA:
columns = {columns}
ownership_percentages = {ownership_percentages}

INSTRUCTIONS:
1. Check if 'OWNERSHIP_PERCENTAGE' is in the list of columns.
2. If NOT present, return:
   {"is_correct": true, "why": "Column OWNERSHIP_PERCENTAGE is not present, check skipped"}
3. If present, compute the sum of ownership_percentages.
4. If the sum is between 99.99 and 100.01 (inclusive), return:
   {"is_correct": true, "why": "Sum of OWNERSHIP_PERCENTAGE is exactly 100"}
5. If the sum is outside that range, return:
   {"is_correct": false, "why": "Sum of OWNERSHIP_PERCENTAGE is {actual_sum}, which is not 100"}

RESPONSE FORMAT:
- Respond with only a valid JSON object.
- No explanations or text.
- No markdown (e.g., no ```json).
- No quotes around true/false.
- Do not return multiple JSONs or arrays.

ONLY ONE JSON RESPONSE:
{"is_correct": true, "why": "reason"}
OR
{"is_correct": false, "why": "reason"}
